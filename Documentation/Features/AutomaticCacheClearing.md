# Automatic Cache Clearing

The Flight Landing Pages extension automatically clears template page caches when destination pairs (flight routes) are edited.

## How It Works

When a destination pair is edited in the TYPO3 backend, the system automatically:

1. **Detects the change** - The `DataHandlerHook` intercepts flight route save operations
2. **Identifies the landing page** - Uses the route's `pid` field to find the parent landing page
3. **Resolves the template page** - Uses the route's `originType` and `destinationType` to find the appropriate template page:
   - First checks for a specific template mapping for the type combination (e.g., Airport → Country)
   - Falls back to the default template page if no specific mapping exists
4. **Clears the cache** - Clears the frontend cache for the resolved template page

## Example Scenarios

### Scenario 1: Specific Template Mapping
- **Route**: IST (Airport) → BG (Country) 
- **Landing Page**: `/flights/poleti/` (UID: 123)
- **Template Mapping**: Airport → Country maps to Template Page UID 456
- **Result**: Cache for Template Page 456 is cleared

### Scenario 2: Default Template
- **Route**: BER (Airport) → SOF (Airport)
- **Landing Page**: `/flights/poleti/` (UID: 123) 
- **Template Mapping**: No specific mapping for Airport → Airport
- **Default Template**: Template Page UID 789 (set in landing page configuration)
- **Result**: Cache for Template Page 789 is cleared

## Technical Implementation

The automatic cache clearing is implemented in:

- **File**: `Classes/Hooks/DataHandlerHook.php`
- **Method**: `handleFlightRouteUpdate()`
- **Hook**: `processDatamap_afterDatabaseOperations`
- **Trigger**: Any save operation on `tx_landingpages_domain_model_flightroute` table

### Dependencies

The implementation uses these services:
- `TemplateResolutionService` - To resolve the correct template page
- `CacheManager` - To clear the frontend cache
- `ConnectionPool` - To query flight route data

### Cache Clearing Strategy

The system clears:
1. **Page cache** - Using tag `pageId_{templatePageUid}`
2. **Hash cache** - General cache that might contain related data

## Benefits

- **Automatic** - No manual cache clearing required
- **Targeted** - Only clears cache for affected template pages
- **Efficient** - Uses TYPO3's tag-based cache clearing
- **Safe** - Errors don't break the save operation

## Configuration

No additional configuration is required. The feature works automatically when:
- The extension is installed and active
- Flight routes are properly configured with landing pages
- Template pages are set up (either default or with specific mappings)

## Troubleshooting

If cache clearing doesn't work:

1. **Check logs** - Look for errors in TYPO3 system logs
2. **Verify template configuration** - Ensure template pages are properly configured
3. **Test manually** - Use the manual cache clearing buttons in the backend
4. **Clear all caches** - Run `ddev exec vendor/bin/typo3 cache:flush`

## Related Features

- [Template Page Mappings](TemplatePageMappings.md)
- [Manual Cache Clearing](ManualCacheClearing.md)
- [Backend Cache Buttons](BackendCacheButtons.md)
